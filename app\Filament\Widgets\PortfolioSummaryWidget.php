<?php

namespace App\Filament\Widgets;

use App\Models\FixedDeposit;
use Illuminate\Support\Facades\DB;
use Filament\Widgets\Widget;

class PortfolioSummaryWidget extends Widget
{
    protected static string $view = 'filament.widgets.portfolio-summary-widget';
    protected static ?int $sort = 5;
    protected int | string | array $columnSpan = 'full';

    public function getViewData(): array
    {
        $summary = [
            'total_deposits' => FixedDeposit::where('status', 'ACTIVE')->count(),
            'total_value' => FixedDeposit::where('status', 'ACTIVE')->sum('principal_amount'),
            'mudharabah_count' => FixedDeposit::where('status', 'ACTIVE')->where('deposit_type', 'MUDHARABAH')->count(),
            'conventional_count' => FixedDeposit::where('status', 'ACTIVE')->where('deposit_type', 'CONVENTIONAL')->count(),
            'avg_tenure' => FixedDeposit::where('status', 'ACTIVE')->avg('tenure_days'),
            'highest_rate' => FixedDeposit::where('status', 'ACTIVE')->max('interest_rate'),
            'lowest_rate' => FixedDeposit::where('status', 'ACTIVE')->min('interest_rate'),
        ];

        $topBanks = DB::table('fixed_deposits')
            ->join('deposit_accounts', 'fixed_deposits.deposit_account_id', '=', 'deposit_accounts.id')
            ->join('bank_locations', 'deposit_accounts.bank_location_id', '=', 'bank_locations.id')
            ->join('banks', 'bank_locations.bank_id', '=', 'banks.id')
            ->where('fixed_deposits.status', 'ACTIVE')
            ->select('banks.bank_name', DB::raw('COUNT(*) as count'), DB::raw('SUM(principal_amount) as total'))
            ->groupBy('banks.id', 'banks.bank_name')
            ->orderBy('total', 'desc')
            ->limit(5)
            ->get();

        return [
            'summary' => $summary,
            'top_banks' => $topBanks,
        ];
    }
}
