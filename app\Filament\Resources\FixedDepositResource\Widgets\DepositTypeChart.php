<?php

namespace App\Filament\Resources\FixedDepositResource\Widgets;

use App\Models\FixedDeposit;
use Filament\Widgets\ChartWidget;

class DepositTypeChart extends ChartWidget
{
    protected static ?string $heading = 'Deposits by Type';
    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $data = FixedDeposit::selectRaw('deposit_type, COUNT(*) as count')
            ->groupBy('deposit_type')
            ->pluck('count', 'deposit_type');

        return [
            'datasets' => [
                [
                    'data' => $data->values()->toArray(),
                    'backgroundColor' => ['#3b82f6', '#10b981'],
                ],
            ],
            'labels' => $data->keys()->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }
}
