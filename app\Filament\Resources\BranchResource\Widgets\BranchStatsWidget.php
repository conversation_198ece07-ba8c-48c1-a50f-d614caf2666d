<?php

namespace App\Filament\Resources\BranchResource\Widgets;

use App\Models\Branch;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class BranchStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total Branches', Branch::count())
                ->description('Registered branches')
                ->descriptionIcon('heroicon-m-building-office-2')
                ->color('success'),

            Stat::make('Active Branches', Branch::whereHas('banks')->count())
                ->description('Branches with bank locations')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('info'),

            Stat::make('Avg Banks per Branch',
                number_format(Branch::withCount('banks')
                    ->get()
                    ->avg('banks_count') ?? 0, 1))
                ->description('Average associations')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('warning'),
        ];
    }
}

