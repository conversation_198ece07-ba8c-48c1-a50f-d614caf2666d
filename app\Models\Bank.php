<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Bank extends Model
{
    use HasFactory;

    protected $fillable = [
        'bank_code',
        'bank_name',
    ];

    public function branches()
    {
        return $this->belongsToMany(Branch::class, 'bank_locations', 'bank_id', 'branch_id')
                    ->withPivot(['address_1', 'address_2', 'address_3', 'address_4', 'bank_officer', 'contact_number', 'created_at', 'updated_at']);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }
}
