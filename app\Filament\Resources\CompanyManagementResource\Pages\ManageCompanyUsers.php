<?php

namespace App\Filament\Resources\CompanyManagementResource\Pages;

use App\Filament\Resources\CompanyManagementResource;
use App\Models\Company;
use App\Models\User;
use App\Models\Role;
use Filament\Actions;
use Filament\Forms;
use Filament\Resources\Pages\Page;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Database\Eloquent\Builder;
use Filament\Notifications\Notification;

class ManageCompanyUsers extends Page implements HasTable
{
    use InteractsWithTable;

    protected static string $resource = CompanyManagementResource::class;

    protected static string $view = 'filament.resources.company-management-resource.pages.manage-company-users';

    public Company $record;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('assignUser')
                ->label('Assign User to Company')
                ->icon('heroicon-o-user-plus')
                ->form([
                    Forms\Components\Select::make('user_id')
                        ->label('User')
                        ->options(
                            User::whereDoesntHave('companies', function ($query) {
                                $query->where('company_id', $this->record->id);
                            })->pluck('name', 'id')
                        )
                        ->searchable()
                        ->required(),
                    Forms\Components\Select::make('roles')
                        ->label('Roles')
                        ->multiple()
                        ->options(
                            Role::where('company_id', $this->record->id)
                                ->pluck('name', 'id')
                        )
                        ->preload(),
                ])
                ->action(function (array $data): void {
                    $user = User::find($data['user_id']);

                    // Assign user to company
                    $this->record->users()->attach($user->id);

                    // Set tenant context and assign roles
                    if (!empty($data['roles'])) {
                        setPermissionsTeamId($this->record->id);
                        $roles = Role::whereIn('id', $data['roles'])->get();
                        $user->assignRole($roles);
                    }

                    Notification::make()
                        ->title('User assigned successfully!')
                        ->success()
                        ->send();
                }),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                User::query()
                    ->whereHas('companies', function ($query) {
                        $query->where('company_id', $this->record->id);
                    })
            )
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('company_roles')
                    ->label('Roles in this Company')
                    ->badge()
                    ->separator(',')
                    ->getStateUsing(function (User $record): array {
                        setPermissionsTeamId($this->record->id);
                        return $record->getRoleNames()->toArray();
                    })
                    ->color('success'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('editRoles')
                    ->label('Edit Roles')
                    ->icon('heroicon-o-pencil')
                    ->form([
                        Forms\Components\Select::make('roles')
                            ->label('Roles')
                            ->multiple()
                            ->options(
                                Role::where('company_id', $this->record->id)
                                    ->pluck('name', 'id')
                            )
                            ->default(function (User $record): array {
                                setPermissionsTeamId($this->record->id);
                                return $record->roles()
                                    ->where('roles.company_id', $this->record->id)
                                    ->pluck('roles.id')
                                    ->toArray();
                            }),
                    ])
                    ->action(function (User $record, array $data): void {
                        setPermissionsTeamId($this->record->id);

                        // Remove all current roles for this company
                        $currentRoles = $record->roles()
                            ->where('roles.company_id', $this->record->id)
                            ->get();
                        $record->removeRole($currentRoles);

                        // Assign new roles
                        if (!empty($data['roles'])) {
                            $newRoles = Role::whereIn('id', $data['roles'])->get();
                            $record->assignRole($newRoles);
                        }

                        Notification::make()
                            ->title('Roles updated successfully!')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\Action::make('removeFromCompany')
                    ->label('Remove from Company')
                    ->icon('heroicon-o-user-minus')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->action(function (User $record): void {
                        // Set tenant context
                        setPermissionsTeamId($this->record->id);

                        // Remove all roles for this company
                        $companyRoles = $record->roles()
                            ->where('roles.company_id', $this->record->id)
                            ->get();
                        $record->removeRole($companyRoles);

                        // Remove user from company
                        $this->record->users()->detach($record->id);

                        Notification::make()
                            ->title('User removed from company successfully!')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('removeFromCompany')
                        ->label('Remove from Company')
                        ->icon('heroicon-o-user-minus')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->action(function ($records): void {
                            setPermissionsTeamId($this->record->id);

                            foreach ($records as $user) {
                                // Remove all roles for this company
                                $companyRoles = $user->roles()
                                    ->where('roles.company_id', $this->record->id)
                                    ->get();
                                $user->removeRole($companyRoles);

                                // Remove user from company
                                $this->record->users()->detach($user->id);
                            }

                            Notification::make()
                                ->title('Users removed from company successfully!')
                                ->success()
                                ->send();
                        }),
                ]),
            ]);
    }
}
