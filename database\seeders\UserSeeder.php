<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Company;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // Create a test company
        $company = Company::firstOrCreate([
            'company_code' => 'TEST',
            'company_name' => 'TEST COMPANY SDN BHD',
            'slug' => 'test-company-sdn-bhd',
        ]);

        // Create super admin user
        $superAdmin = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Super Admin',
            'password' => Hash::make('password'),
        ]);

        // Attach user to company first
        if (!$superAdmin->companies->contains($company)) {
            $superAdmin->companies()->attach($company);
        }

        // Set the team context and assign super admin role
        setPermissionsTeamId($company->id);
        $superAdmin->assignRole('super_admin');

        // Create a regular user
        $user = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Regular User',
            'password' => Hash::make('password'),
        ]);

        // Attach user to company first
        if (!$user->companies->contains($company)) {
            $user->companies()->attach($company);
        }

        // Set the team context and assign panel user role
        setPermissionsTeamId($company->id);
        $user->assignRole('panel_user');

        $this->command->info('Users created successfully!');
        $this->command->info('Super Admin: <EMAIL> / password');
        $this->command->info('Regular User: <EMAIL> / password');
    }
}
