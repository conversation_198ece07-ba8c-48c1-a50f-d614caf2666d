<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CompanyManagementResource\Pages;
use App\Models\Company;
use App\Models\User;
use App\Models\Role;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Facades\Filament;

class CompanyManagementResource extends Resource
{
    protected static ?string $model = Company::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?string $navigationGroup = 'Company Management';

    protected static ?string $navigationLabel = 'Company Management';

    protected static ?string $modelLabel = 'Company';

    protected static ?string $pluralModelLabel = 'Companies';

    // Disable tenant scoping for this resource - we want to see ALL companies
    protected static ?string $tenantOwnershipRelationshipName = null;

    // Override to show ALL companies
    public static function getEloquentQuery(): Builder
    {
        // Return all companies without any tenant filtering - bypass parent completely
        return static::getModel()::query();
    }

    // Override to disable tenant scoping completely
    public static function isScopedToTenant(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Company Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(1000),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('User Management')
                    ->schema([
                        Forms\Components\Repeater::make('userAssignments')
                            ->relationship('users')
                            ->schema([
                                Forms\Components\Select::make('user_id')
                                    ->label('User')
                                    ->options(User::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required(),
                                Forms\Components\Select::make('roles')
                                    ->label('Roles')
                                    ->multiple()
                                    ->options(function (callable $get) {
                                        $companyId = $get('../../id');
                                        if (!$companyId) return [];
                                        
                                        return Role::where('company_id', $companyId)
                                            ->pluck('name', 'id');
                                    })
                                    ->preload(),
                            ])
                            ->columns(2)
                            ->addActionLabel('Add User')
                            ->collapsible(),
                    ])
                    ->visible(fn ($record) => $record !== null), // Only show when editing existing company
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('slug')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('users_count')
                    ->counts('users')
                    ->label('Users')
                    ->badge()
                    ->color('info'),
                Tables\Columns\TextColumn::make('roles_count')
                    ->counts('roles')
                    ->label('Roles')
                    ->badge()
                    ->color('success'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('manageUsers')
                    ->label('Manage Users')
                    ->icon('heroicon-o-users')
                    ->color('info')
                    ->url(fn (Company $record): string => static::getUrl('manageUsers', ['record' => $record])),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompanyManagement::route('/'),
            'create' => Pages\CreateCompanyManagement::route('/create'),
            'view' => Pages\ViewCompanyManagement::route('/{record}'),
            'edit' => Pages\EditCompanyManagement::route('/{record}/edit'),
            'manageUsers' => Pages\ManageCompanyUsers::route('/{record}/users'),
        ];
    }
}
