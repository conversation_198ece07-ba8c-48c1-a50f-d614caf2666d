<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListRoles extends ListRecords
{
    protected static string $resource = RoleResource::class;



    public function getTitle(): string
    {
        return 'Roles';
    }

    public function getHeading(): string
    {
        return 'Roles';
    }

    public function getSubheading(): ?string
    {
        return 'Manage roles and their permissions';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('New Role')
                ->icon('heroicon-m-plus')
                ->color('primary'),
        ];
    }
}
