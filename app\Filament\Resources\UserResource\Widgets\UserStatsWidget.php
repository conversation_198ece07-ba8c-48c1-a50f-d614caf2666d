<?php

namespace App\Filament\Resources\UserResource\Widgets;

use App\Models\User;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class UserStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total Users', User::count())
                ->description('Registered users')
                ->descriptionIcon('heroicon-m-users')
                ->color('success'),

            Stat::make('Active Users', User::where('email_verified_at', '!=', null)->count())
                ->description('Verified accounts')
                ->descriptionIcon('heroicon-m-check-badge')
                ->color('info'),

            Stat::make('New This Month',
                User::where('created_at', '>=', Carbon::now()->startOfMonth())->count())
                ->description('New registrations')
                ->descriptionIcon('heroicon-m-user-plus')
                ->color('warning'),
        ];
    }
}
