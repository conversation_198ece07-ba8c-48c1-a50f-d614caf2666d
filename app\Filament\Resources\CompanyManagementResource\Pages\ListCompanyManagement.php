<?php

namespace App\Filament\Resources\CompanyManagementResource\Pages;

use App\Filament\Resources\CompanyManagementResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCompanyManagement extends ListRecords
{
    protected static string $resource = CompanyManagementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
