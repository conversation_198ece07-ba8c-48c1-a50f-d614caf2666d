<?php

namespace App\Filament\Resources\BankResource\Widgets;

use App\Models\Bank;
use Filament\Widgets\ChartWidget;

class BankDistributionChart extends ChartWidget
{
    protected static ?string $heading = 'Banks by Location Count';
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $maxHeight = '300px';

    protected function getData(): array
    {
        $data = Bank::withCount('branches')
            ->get()
            ->groupBy('branches_count')
            ->map(fn($group) => $group->count())
            ->sortKeys();

        return [
            'datasets' => [
                [
                    'label' => 'Number of Banks',
                    'data' => $data->values()->toArray(),
                    'backgroundColor' => ['#f59e0b', '#3b82f6', '#10b981', '#ef4444'],
                ],
            ],
            'labels' => $data->keys()->map(fn($count) => $count . ' locations')->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }
}
