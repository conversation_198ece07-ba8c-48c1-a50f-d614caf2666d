<?php

namespace App\Filament\Resources\UserManagementResource\Pages;

use App\Filament\Resources\UserManagementResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Facades\Filament;

class EditUserManagement extends EditRecord
{
    protected static string $resource = UserManagementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load existing role assignments for the form
        $currentTenant = Filament::getTenant();
        $user = $this->record;
        
        $roleAssignments = [];
        
        foreach ($user->companies as $company) {
            setPermissionsTeamId($company->id);
            $roles = $user->roles()->pluck('roles.id')->toArray();
            
            if (!empty($roles)) {
                $roleAssignments[] = [
                    'company_id' => $company->id,
                    'roles' => $roles,
                ];
            }
        }
        
        $data['role_assignments'] = $roleAssignments;
        
        return $data;
    }

    protected function afterSave(): void
    {
        $data = $this->form->getState();
        $user = $this->record;
        
        // Handle role assignments
        if (isset($data['role_assignments'])) {
            foreach ($data['role_assignments'] as $assignment) {
                $companyId = $assignment['company_id'];
                $roleIds = $assignment['roles'] ?? [];
                
                // Set tenant context for this company
                setPermissionsTeamId($companyId);
                
                // Remove all existing roles for this company
                $user->roles()->detach();
                
                // Assign new roles for this company
                if (!empty($roleIds)) {
                    $user->assignRole($roleIds);
                }
            }
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
