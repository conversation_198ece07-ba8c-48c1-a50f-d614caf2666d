<?php

namespace App\Traits;

trait HasTenantPolicy
{
    /**
     * Set the tenant context for permission checks
     */
    protected function setTenantContext(): void
    {
        $tenant = \Filament\Facades\Filament::getTenant();
        if ($tenant) {
            setPermissionsTeamId($tenant->id);
        }
    }

    /**
     * Check permission with tenant context
     */
    protected function checkPermission($user, string $permission): bool
    {
        $this->setTenantContext();
        return $user->can($permission);
    }
}
