<?php

if (!function_exists('getPermissionsTeamId')) {
    /**
     * Get the current tenant/team ID for permissions
     * This function returns the current tenant ID for use with Spatie Permission teams feature
     */
    function getPermissionsTeamId(): ?int
    {
        // Check if we're in a Filament context and have a tenant
        if (app()->bound('filament.tenant')) {
            $tenant = app('filament.tenant');
            return $tenant?->id;
        }

        // Fallback: try to get from Filament facade
        if (class_exists(\Filament\Facades\Filament::class)) {
            $tenant = \Filament\Facades\Filament::getTenant();
            return $tenant?->id;
        }

        // Default fallback
        return null;
    }
}
