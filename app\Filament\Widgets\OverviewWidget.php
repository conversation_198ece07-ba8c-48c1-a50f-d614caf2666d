<?php

namespace App\Filament\Widgets;

use App\Models\FixedDeposit;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Number;

class OverviewWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';
    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        $totalActive = FixedDeposit::where('status', 'ACTIVE')->count() ?? 0;
        $totalValue = FixedDeposit::where('status', 'ACTIVE')->sum('principal_amount') ?? 0;
        $averageRate = FixedDeposit::where('status', 'ACTIVE')->avg('interest_rate') ?? 0;
        $maturingThisMonth = FixedDeposit::where('status', 'ACTIVE')
            ->whereBetween('maturity_date', [now()->startOfMonth(), now()->endOfMonth()])
            ->count();

        return [
            Stat::make('Active Deposits', Number::format($totalActive))
                ->description('Currently active fixed deposits')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success')
                ->chart([7, 3, 4, 5, 6, 3, 5]),

            Stat::make('Total Value', 'RM ' . Number::format($totalValue, 2))
                ->description('Total principal amount')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('primary')
                ->chart([15, 4, 10, 2, 12, 4, 12]),

            Stat::make('Average Rate', Number::format($averageRate, 2) . '%')
                ->description('Average interest rate')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('info'),

            Stat::make('Maturing This Month', Number::format($maturingThisMonth))
                ->description('Deposits maturing soon')
                ->descriptionIcon('heroicon-m-clock')
                ->color($maturingThisMonth > 0 ? 'warning' : 'success'),
        ];
    }
}

