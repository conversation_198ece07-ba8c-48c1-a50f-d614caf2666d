<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'User Management';

    protected static ?int $navigationSort = 1;

    protected static ?string $tenantOwnershipRelationshipName = 'companies';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                ->required()
                ->maxLength(20),
                TextInput::make('email')
                ->required()
                ->email()
                ->unique(ignoreRecord: true)
                ->maxLength(30),
                // ->suffix('.com')
                // ->dehydrateStateUsing(fn ($state) => str_ends_with($state, '.com') ? $state : $state . '.com'),
                TextInput::make('password')
                ->required(fn (string $context): bool => $context === 'create')
                ->password()
                ->dehydrated(fn ($state) => filled($state))
                // ->dehydrateStateUsing(fn ($state) => bcrypt($state))
                ->revealable()
                ->maxLength(20),
                Select::make('roles')
                ->relationship('roles', 'name')
                ->saveRelationshipsUsing(function (Model $record, $state) {
                    $record->roles()->syncWithPivotValues($state, ['company_id' => getPermissionsTeamId()]);
                })
                ->multiple()
                ->preload()
                ->searchable(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                ->sortable()
                ->searchable()
                ->toggleable(),
                TextColumn::make('name')
                ->sortable()
                ->searchable()
                ->toggleable(),
                TextColumn::make('email')
                ->sortable()
                ->searchable()
                ->toggleable(),
                TextColumn::make('roles.name')
                ->badge()
                ->color(function (string $state): string {
                    return match ($state) {
                        'admin' => 'danger',
                        'user' => 'gray',
                        'editor' => 'info',
                        'moderator' => 'success',
                        'banned' => 'warning',
                        default => 'primary',
                    };

                })
                ->sortable()
                ->searchable()
                ->toggleable(),
                TextColumn::make('created_at')->date()
                ->sortable()
                ->searchable()
                ->toggleable(),
                TextColumn::make('updated_at')->date()
                ->sortable()
                ->searchable()
                ->toggleable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
