<?php

namespace App\Filament\Resources\Pages\Tenancy;

use App\Models\Company;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\Tenancy\RegisterTenant;
use Illuminate\Support\Facades\Auth;

class RegisterCompany extends RegisterTenant
{
    public static function getLabel(): string
    {
        return 'Register company';
    }

    public function form(Form $form): Form
    {
        return $form
        ->schema([
            Section::make('Company Information')
            ->description('This is the top level resource for managing all your companies.')
            ->schema([
                TextInput::make('company_code')
                ->label('Company Code')
                ->required()
                ->maxLength(10)
                ->unique(ignoreRecord: true)
                ->extraInputAttributes(['onInput' => 'this.value = this.value.toUpperCase()'])
                ->columnSpan('1')
                ->placeholder('e.g. AE'),
                TextInput::make('company_name')
                ->label('Company Name')
                ->required()
                ->maxLength(255)
                ->unique(ignoreRecord: true)
                ->extraInputAttributes(['onInput' => 'this.value = this.value.toUpperCase()'])
                ->columnSpan('1')
                ->placeholder('e.g. RISDA ESTATES SDN. BHD.'),
                TextInput::make('slug')
                ->required()
                ->maxLength(255)
                ->unique(ignoreRecord: true)
                ->columnSpan('1')
                ->placeholder('e.g. risda-estates-sdn-bhd'),
            ])->columns(2),

        ])->columns(1);
    }

    protected function handleRegistration(array $data): Company
    {
        $company = Company::create($data);

        $company->users()->attach(Auth::user());

        return $company;
    }
}
