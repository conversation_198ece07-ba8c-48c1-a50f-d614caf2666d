<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Bank;
use Illuminate\Auth\Access\HandlesAuthorization;

class BankPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_bank');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Bank $bank): bool
    {
        return $this->checkPermission($user, 'view_bank');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $this->checkPermission($user, 'create_bank');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Bank $bank): bool
    {
        return $this->checkPermission($user, 'update_bank');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Bank $bank): bool
    {
        return $this->checkPermission($user, 'delete_bank');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $this->checkPermission($user, 'delete_any_bank');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, Bank $bank): bool
    {
        return $this->checkPermission($user, 'force_delete_bank');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $this->checkPermission($user, 'force_delete_any_bank');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, Bank $bank): bool
    {
        return $this->checkPermission($user, 'restore_bank');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $this->checkPermission($user, 'restore_any_bank');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, Bank $bank): bool
    {
        return $this->checkPermission($user, 'replicate_bank');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $this->checkPermission($user, 'reorder_bank');
    }
}
