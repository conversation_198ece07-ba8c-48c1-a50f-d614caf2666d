<?php

namespace App\Filament\Resources\FixedDepositResource\Widgets;

use App\Models\FixedDeposit;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;

class MonthlyDepositsChart extends ChartWidget
{
    protected static ?string $heading = 'Monthly Deposit Trends';
    protected static ?int $sort = 4;
    protected int | string | array $columnSpan = 'full';

    protected function getData(): array
    {
        $data = FixedDeposit::selectRaw('DATE_FORMAT(start_date, "%Y-%m") as month, COUNT(*) as count, SUM(principal_amount) as amount')
            ->where('start_date', '>=', Carbon::now()->subMonths(12))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Number of Deposits',
                    'data' => $data->pluck('count')->toArray(),
                    'borderColor' => '#3b82f6',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'yAxisID' => 'y',
                ],
                [
                    'label' => 'Total Amount (RM)',
                    'data' => $data->pluck('amount')->toArray(),
                    'borderColor' => '#10b981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'yAxisID' => 'y1',
                ],
            ],
            'labels' => $data->pluck('month')->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
                ],
                'y1' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'right',
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                ],
            ],
        ];
    }
}
