<?php

namespace App\Filament\Resources\BankResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\AttachAction;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BranchesRelationManager extends RelationManager
{
    protected static string $relationship = 'Branches';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('address_line1')
                ->label('Street & Building')
                ->required()
                ->extraInputAttributes(['onInput' => 'this.value = this.value.toUpperCase()'])
                ->placeholder('e.g. JALAN AMPANG ULU 3'),
                TextInput::make('address_line2')
                ->label('Neighborhood / Township')
                ->nullable()
                ->extraInputAttributes(['onInput' => 'this.value = this.value.toUpperCase()'])
                ->placeholder('e.g. KAMPUNG BEREMBANG'),
                TextInput::make('address_line3')
                ->label('Postcode & City')
                ->required()
                ->extraInputAttributes(['onInput' => 'this.value = this.value.toUpperCase()'])
                ->placeholder('e.g. 55000 KUALA LUMPUR'),
                Select::make('address_line4')
                ->label('State')
                ->options([
                    'JOHOR' => 'JOHOR',
                    'KEDAH' => 'KEDAH',
                    'KELANTAN' => 'KELANTAN',
                    'MELAKA' => 'MELAKA',
                    'NEGERI SEMBILAN' => 'NEGERI SEMBILAN',
                    'PAHANG' => 'PAHANG',
                    'PERAK' => 'PERAK',
                    'PERLIS' => 'PERLIS',
                    'PULAU PINANG' => 'PULAU PINANG',
                    'SABAH' => 'SABAH',
                    'SARAWAK' => 'SARAWAK',
                    'SELANGOR' => 'SELANGOR',
                    'TERENGGANU' => 'TERENGGANU',
                    'W.P. KUALA LUMPUR' => 'W.P. KUALA LUMPUR',
                    'W.P. LABUAN' => 'W.P. LABUAN',
                    'W.P. PUTRAJAYA' => 'W.P. PUTRAJAYA',
                ])
                ->searchable()
                ->required(),
                TextInput::make('bank_officer')
                ->label('Bank Officer')
                ->nullable()
                ->extraInputAttributes(['onInput' => 'this.value = this.value.toUpperCase()'])
                ->maxLength(20)
                ->placeholder('e.g. PUAN NOR AZLINA'),
                TextInput::make('contact_number')
                ->label('Contact No.')
                ->tel()
                ->nullable()
                ->maxLength(10)
                ->placeholder('e.g. **********'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('branch_name') //don't use pivot column
            ->columns([
                Tables\Columns\TextColumn::make('branch_name'),
                Tables\Columns\TextColumn::make('address_line1'),
                Tables\Columns\TextColumn::make('address_line2'),
                Tables\Columns\TextColumn::make('address_line3'),
                Tables\Columns\TextColumn::make('address_line4'),
                Tables\Columns\TextColumn::make('bank_officer'),
                Tables\Columns\TextColumn::make('contact_number'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                // Tables\Actions\CreateAction::make(),
                Tables\Actions\AttachAction::make()
                ->preloadRecordSelect()
                ->form(fn (AttachAction $action): array => [
                    $action->getRecordSelect(),
                    TextInput::make('address_line1')
                    ->label('Street & Building')
                    ->required()
                    ->extraInputAttributes(['onInput' => 'this.value = this.value.toUpperCase()'])
                    ->placeholder('e.g. JALAN AMPANG ULU 3'),
                    TextInput::make('address_line2')
                    ->label('Neighborhood / Township')
                    ->nullable()
                    ->extraInputAttributes(['onInput' => 'this.value = this.value.toUpperCase()'])
                    ->placeholder('e.g. KAMPUNG BEREMBANG'),
                    TextInput::make('address_line3')
                    ->label('Postcode & City')
                    ->required()
                    ->extraInputAttributes(['onInput' => 'this.value = this.value.toUpperCase()'])
                    ->placeholder('e.g. 55000 KUALA LUMPUR'),
                    Select::make('address_line4')
                    ->label('State')
                    ->options([
                        'JOHOR' => 'JOHOR',
                        'KEDAH' => 'KEDAH',
                        'KELANTAN' => 'KELANTAN',
                        'MELAKA' => 'MELAKA',
                        'NEGERI SEMBILAN' => 'NEGERI SEMBILAN',
                        'PAHANG' => 'PAHANG',
                        'PERAK' => 'PERAK',
                        'PERLIS' => 'PERLIS',
                        'PULAU PINANG' => 'PULAU PINANG',
                        'SABAH' => 'SABAH',
                        'SARAWAK' => 'SARAWAK',
                        'SELANGOR' => 'SELANGOR',
                        'TERENGGANU' => 'TERENGGANU',
                        'W.P. KUALA LUMPUR' => 'W.P. KUALA LUMPUR',
                        'W.P. LABUAN' => 'W.P. LABUAN',
                        'W.P. PUTRAJAYA' => 'W.P. PUTRAJAYA',
                    ])
                    ->searchable()
                    ->required(),
                    TextInput::make('bank_officer')
                    ->label('Bank Officer')
                    ->nullable()
                    ->maxLength(20)
                    ->placeholder('e.g. PUAN NOR AZLINA'),
                    TextInput::make('contact_number')
                    ->label('Contact No.')
                    ->tel()
                    ->nullable()
                    ->maxLength(10)
                    ->placeholder('e.g. **********'),
                ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
                Tables\Actions\DetachAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }
}
