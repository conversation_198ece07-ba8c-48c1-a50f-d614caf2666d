<?php

namespace App\Filament\Exports;

use App\Models\FixedDeposit;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Illuminate\Database\Eloquent\Builder;

class FixedDepositExporter extends Exporter
{
    protected static ?string $model = FixedDeposit::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('company.company_name')
                ->label('Company Name'),
            ExportColumn::make('depositAccount.account_number')
                ->label('Account No.'),
            ExportColumn::make('depositAccount.bankLocation.bank.bank_name')
                ->label('Bank'),
            ExportColumn::make('depositAccount.bankLocation.branch.branch_name')
                ->label('Branch'),
            ExportColumn::make('receipt_number')
                ->label('Receipt No.'),
            ExportColumn::make('principal_amount')
                ->label('Principal Amount')
                ->formatStateUsing(fn ($state) => number_format($state, 2)),
            ExportColumn::make('interest_rate')
                ->label('Interest Rate')
                ->formatStateUsing(fn ($state) => $state . '%'),
            ExportColumn::make('tenure_days')
                ->label('Tenure')
                ->formatStateUsing(fn ($state) => $state . ' days'),
            ExportColumn::make('start_date')
                ->label('Start Date')
                ->formatStateUsing(fn ($state) => $state->format('d/m/Y')),
            ExportColumn::make('maturity_date')
                ->label('Maturity Date')
                ->formatStateUsing(fn ($state) => $state->format('d/m/Y')),
            ExportColumn::make('profit_sharing_client')
                ->label('Profit Sharing (Client/Bank)')
                ->formatStateUsing(fn ($state) => $state . '/' . (100 - $state)),
            ExportColumn::make('deposit_type')
                ->label('Deposit Type')
                ->formatStateUsing(fn ($state) => match ($state) {
                    'MUDHARABAH' => 'Mudharabah',
                    'CONVENTIONAL' => 'Conventional',
                    default => 'Unknown',
                }),
            ExportColumn::make('notes')
                ->label('Notes'),
            ExportColumn::make('total_profit_amount')
                ->label('Total Profit')
                ->formatStateUsing(fn ($state) => number_format($state, 2)),
            ExportColumn::make('client_profit_amount')
                ->label('Client Profit')
                ->formatStateUsing(fn ($state) => number_format($state, 2)),
            ExportColumn::make('bank_profit_amount')
                ->label('Bank Profit')
                ->formatStateUsing(fn ($state) => number_format($state, 2)),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your fixed deposit export has completed and ' . number_format($export->successful_rows) . ' ' . str('row')->plural($export->successful_rows) . ' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to export.';
        }

        return $body;
    }
}

