<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Company extends Model
{
    protected $fillable = [
        'company_name',
        'company_code',
        'slug',
    ];

    protected $appends = ['name'];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    // Accessor for the name attribute (required by Filament)
    public function getNameAttribute(): string
    {
        return $this->company_name ?? 'Unnamed Company';
    }

    // Tenant interface methods
    public function getTenantName(): string
    {
        return $this->company_name ?? 'Unnamed Company';
    }

    public function getRouteKey()
    {
        return $this->slug;
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
