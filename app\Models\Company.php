<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Company extends Model
{
    protected $fillable = [
        'company_name',
        'company_code',
        'slug',
    ];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    // Tenant interface methods
    public function getTenantName(): string
    {
        return $this->company_name;
    }

    public function getRouteKey()
    {
        return $this->slug;
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
