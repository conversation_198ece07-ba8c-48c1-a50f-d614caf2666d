<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Company extends Model
{
    protected $fillable = [
        'company_name',
        'company_code',
        'slug',
    ];

    protected $appends = ['name'];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    public function banks(): HasMany
    {
        return $this->hasMany(Bank::class);
    }

    public function branches(): HasMany
    {
        return $this->hasMany(Branch::class);
    }

    public function bankLocations(): HasMany
    {
        return $this->hasMany(BankLocation::class);
    }

    public function depositAccounts(): Has<PERSON><PERSON>
    {
        return $this->hasMany(DepositAccount::class);
    }

    public function fixedDeposits(): HasMany
    {
        return $this->hasMany(FixedDeposit::class);
    }

    public function roles(): Has<PERSON>any
    {
        return $this->hasMany(Role::class);
    }

    // Accessor for the name attribute (required by Filament)
    public function getNameAttribute(): string
    {
        return $this->company_name ?? 'Unnamed Company';
    }

    // Tenant interface methods
    public function getTenantName(): string
    {
        return $this->company_name ?? 'Unnamed Company';
    }

    public function getRouteKey()
    {
        return $this->slug;
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
