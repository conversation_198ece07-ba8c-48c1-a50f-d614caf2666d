<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Fixed Deposits Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 15px;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h2 {
            color: #ff0000;
            margin-bottom: 5px;
        }

        .header p {
            margin: 3px 0;
            color: #ff0000;
        }

        .filters {
            text-align: center;
            margin-bottom: 10px;
            font-size: 10px;
            font-style: italic;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            border: 1px solid #000;
        }

        th {
            border-bottom: 1px solid #000;
            padding: 5px;
            text-align: left;
            font-weight: bold;
        }

        td {
            padding: 5px;
            text-align: left;
            border: none;
        }

        .footer {
            margin-top: 20px;
            text-align: right;
            font-size: 10px;
        }

        .summary {
            margin-top: 20px;
            font-weight: bold;
        }

        .date-info {
            position: running(dateInfo);
            text-align: left;
            font-size: 10px;
        }

        @page {
            margin: 15mm 10mm 15mm 10mm;

            @top-left {
                content: element(dateInfo);
            }
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }
    </style>
</head>

<body>
    <div class="date-info">
        Generated on: {{ $date }}
    </div>

    <div class="header">
        <h2>Fixed Deposits Report</h2>
        <p>{{ $companyInfo }}</p>
        <p>{{ $depositType }}</p>
    </div>

    @if (isset($filters) && count($filters) > 0)
        <div class="filters">
            @foreach ($filters as $filter)
                <span>{{ $filter }}</span>
                @if (!$loop->last)
                    |
                @endif
            @endforeach
        </div>
    @endif

    <table>
        <thead>
            <tr>
                <th>Start Date</th>
                <th>Maturity Date</th>
                <th>Bank</th>
                <th>Branch</th>
                <th>Principal (RM)</th>
                <th>Rate (%)</th>
                <th>Profit Sharing (Client/Bank)</th>
                <th>Tenure</th>
                <th>Receipt No.</th>
                <th>Profit Client (RM)</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($records as $deposit)
                <tr>
                    <td>{{ $deposit->start_date->format('d/m/Y') }}</td>
                    <td>{{ $deposit->maturity_date->format('d/m/Y') }}</td>
                    <td>{{ $deposit->depositAccount->bankLocation->bank->bank_name ?? 'N/A' }}</td>
                    <td>{{ $deposit->depositAccount->bankLocation->branch->branch_name ?? 'N/A' }}</td>
                    <td class="text-right">{{ number_format($deposit->principal_amount, 2) }}</td>
                    <td class="text-center">{{ $deposit->interest_rate }}</td>
                    <td class="text-center">
                        {{ $deposit->profit_sharing_client }}/{{ 100 - $deposit->profit_sharing_client }}</td>
                    <td class="text-center">{{ $deposit->tenure_days }} days</td>
                    <td>{{ $deposit->receipt_number }}</td>
                    <td class="text-right">{{ number_format($deposit->client_profit_amount, 2) }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <div class="summary">
        <p>Total Deposits: {{ $records->count() }}</p>
        <p>Total Principal: RM {{ number_format($records->sum('principal_amount'), 2) }}</p>
        <p>Total Client Profit: RM {{ number_format($records->sum('client_profit_amount'), 2) }}</p>
    </div>

    <div class="footer">
        <p>This is a computer-generated document. No signature is required.</p>
    </div>

    <script type="text/php">
        if (isset($pdf)) {
            $text = "Page {PAGE_NUM} of {PAGE_COUNT}";
            $size = 10;
            $font = $fontMetrics->getFont("Arial");
            $width = $fontMetrics->get_text_width($text, $font, $size) / 2;
            $x = $pdf->get_width() - 72;
            $y = 30;
            $pdf->page_text($x, $y, $text, $font, $size);
        }
    </script>
</body>

</html>
