<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use Filament\Actions;
use Filament\Facades\Filament;
use Filament\Resources\Pages\EditRecord;

class EditRole extends EditRecord
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $tenant = Filament::getTenant();
        if ($tenant) {
            setPermissionsTeamId($tenant->id);
        }

        // Load current permissions
        $data['permissions'] = $this->record->permissions->pluck('name')->toArray();

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $tenant = Filament::getTenant();
        if ($tenant) {
            setPermissionsTeamId($tenant->id);
        }

        return $data;
    }

    protected function afterSave(): void
    {
        $tenant = Filament::getTenant();
        if ($tenant) {
            setPermissionsTeamId($tenant->id);
        }

        // Get permissions from form data
        $permissions = collect($this->form->getState())
            ->filter(function ($value, $key) {
                return $key === 'permissions' && is_array($value);
            })
            ->flatten()
            ->filter()
            ->toArray();

        $this->record->syncPermissions($permissions);
    }
}
