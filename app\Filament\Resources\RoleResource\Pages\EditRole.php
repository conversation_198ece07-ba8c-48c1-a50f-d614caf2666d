<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use Filament\Actions;
use Filament\Facades\Filament;
use Filament\Resources\Pages\EditRecord;

class EditRole extends EditRecord
{
    protected static string $resource = RoleResource::class;

    public function getTitle(): string
    {
        return "Edit Role: {$this->record->name}";
    }

    public function getHeading(): string
    {
        return "Edit Role: {$this->record->name}";
    }

    public function getSubheading(): ?string
    {
        return 'Modify role permissions and settings';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->requiresConfirmation()
                ->color('danger'),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $tenant = Filament::getTenant();
        if ($tenant) {
            setPermissionsTeamId($tenant->id);
        }

        // Load current permissions
        $data['permissions'] = $this->record->permissions->pluck('name')->toArray();

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $tenant = Filament::getTenant();
        if ($tenant) {
            setPermissionsTeamId($tenant->id);
        }

        return $data;
    }

    protected function afterSave(): void
    {
        $tenant = Filament::getTenant();
        if ($tenant) {
            setPermissionsTeamId($tenant->id);
        }

        // Get permissions from form data
        $permissions = collect($this->form->getState())
            ->filter(function ($value, $key) {
                return $key === 'permissions' && is_array($value);
            })
            ->flatten()
            ->filter()
            ->toArray();

        $this->record->syncPermissions($permissions);
    }
}
