<?php

namespace App\Filament\Resources;

use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Filament\Resources\RoleResource\Pages;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationGroup = 'User Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Role Details')
                    ->description('Configure the basic role information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->label('Role Name')
                                    ->placeholder('Enter role name')
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('guard_name')
                                    ->default('web')
                                    ->required()
                                    ->label('Guard')
                                    ->disabled()
                                    ->dehydrated(),
                            ]),
                    ])
                    ->collapsible()
                    ->persistCollapsed(),

                Forms\Components\Section::make('Permissions')
                    ->description('Select the permissions for this role')
                    ->schema([
                        static::getPermissionFormSchema(),
                    ])
                    ->collapsible()
                    ->persistCollapsed()
                    ->compact(),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Name')
                    ->weight('font-medium')
                    ->formatStateUsing(fn ($state): string => Str::headline($state))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('guard_name')
                    ->label('Guard')
                    ->badge()
                    ->color('warning'),
                Tables\Columns\TextColumn::make('permissions_count')
                    ->counts('permissions')
                    ->label('Permissions')
                    ->badge()
                    ->color('success'),
                Tables\Columns\TextColumn::make('users_count')
                    ->counts('users')
                    ->label('Users')
                    ->badge()
                    ->color('info'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->color('primary'),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation(),
                ]),
            ])
            ->emptyStateHeading('No roles found')
            ->emptyStateDescription('Create your first role to get started.')
            ->emptyStateIcon('heroicon-o-shield-check');
    }

    public static function getEloquentQuery(): Builder
    {
        // Start fresh query instead of using parent
        $query = static::getModel()::query();

        // Get current tenant
        $tenant = Filament::getTenant();

        if ($tenant) {
            // Scope to current tenant's roles only
            $query->where('company_id', $tenant->id);
        }

        return $query;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }

    protected static function getPermissionFormSchema(): Forms\Components\Component
    {
        $tenant = Filament::getTenant();
        if (!$tenant) {
            return Forms\Components\Placeholder::make('no_tenant')
                ->content('No tenant context available');
        }

        // Set tenant context for permissions
        setPermissionsTeamId($tenant->id);

        // Get all permissions and group them properly
        $permissions = Permission::all();

        // Separate resources and widgets
        $resourcePermissions = $permissions->filter(function ($permission) {
            return !str_starts_with($permission->name, 'widget_');
        });

        $widgetPermissions = $permissions->filter(function ($permission) {
            return str_starts_with($permission->name, 'widget_');
        });

        // Group resource permissions by resource name
        $groupedResources = $resourcePermissions->groupBy(function ($permission) {
            $name = $permission->name;

            // Handle complex resource names like bank::location, deposit::account, fixed::deposit
            if (str_contains($name, '::')) {
                $parts = explode('::', $name);
                return $parts[1]; // location, account, deposit
            }

            // Handle standard permissions like view_bank, create_bank, view_any_bank
            $parts = explode('_', $name);

            if (count($parts) >= 2) {
                if ($parts[0] === 'view' && $parts[1] === 'any') {
                    // view_any_bank -> bank
                    return implode('_', array_slice($parts, 2));
                } elseif (in_array($parts[0], ['create', 'update', 'delete', 'restore', 'replicate', 'reorder', 'force'])) {
                    // create_bank, delete_any_bank -> bank
                    if ($parts[1] === 'any') {
                        return implode('_', array_slice($parts, 2));
                    } else {
                        return implode('_', array_slice($parts, 1));
                    }
                } else {
                    // view_bank -> bank
                    return implode('_', array_slice($parts, 1));
                }
            }

            return 'other';
        });

        $tabs = [];

        // Add resource tabs
        foreach ($groupedResources as $resource => $perms) {
            if ($resource === 'other' || empty($resource)) continue;

            $resourceName = str_replace('_', ' ', ucwords(str_replace('_', ' ', $resource)));

            $tabs[] = Forms\Components\Tabs\Tab::make($resource)
                ->label($resourceName)
                ->schema([
                    Forms\Components\CheckboxList::make('permissions')
                        ->options($perms->pluck('name', 'name')->toArray())
                        ->columns(3)
                        ->gridDirection('row')
                        ->hiddenLabel(),
                ]);
        }

        // Add widgets tab if there are widget permissions
        if ($widgetPermissions->isNotEmpty()) {
            $tabs[] = Forms\Components\Tabs\Tab::make('widgets')
                ->label('Widgets')
                ->schema([
                    Forms\Components\CheckboxList::make('permissions')
                        ->options($widgetPermissions->pluck('name', 'name')->toArray())
                        ->columns(2)
                        ->gridDirection('row')
                        ->hiddenLabel(),
                ]);
        }

        return Forms\Components\Tabs::make('Permissions')
            ->tabs($tabs)
            ->contained(false);
    }


}
