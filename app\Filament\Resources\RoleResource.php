<?php

namespace App\Filament\Resources;

use BezhanSalleh\FilamentShield\Resources\RoleResource as ShieldRoleResource;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Permission\Models\Role;

class RoleResource extends ShieldRoleResource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationGroup = 'User Management';

    protected static ?int $navigationSort = 1;

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Get current tenant
        $tenant = Filament::getTenant();

        if ($tenant) {
            // Scope to current tenant's roles
            $query->where('company_id', $tenant->id);
        }

        return $query;
    }

    public static function shouldRegisterNavigation(): bool
    {
        return true;
    }
}
