<?php

namespace App\Filament\Resources;

use BezhanSalleh\FilamentShield\Resources\RoleResource as ShieldRoleResource;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Builder;

class RoleResource extends ShieldRoleResource
{
    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();
        
        // Get current tenant
        $tenant = Filament::getTenant();
        
        if ($tenant) {
            // Scope to current tenant's roles only
            $query->where('company_id', $tenant->id);
        }
        
        return $query;
    }
}
