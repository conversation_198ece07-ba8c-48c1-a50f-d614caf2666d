<?php

namespace App\Filament\Resources;

use BezhanSalleh\FilamentShield\Resources\RoleResource as ShieldRoleResource;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Builder;

class RoleResource extends ShieldRoleResource
{
    protected static ?string $model = \App\Models\Role::class;
    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    protected static ?string $navigationGroup = 'Configuration';
    protected static ?int $navigationSort = 4;
    protected static ?string $tenantOwnershipRelationshipName = 'company';

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Get current tenant
        $tenant = Filament::getTenant();

        if ($tenant) {
            // Scope to current tenant's roles only
            $query->where('company_id', $tenant->id);
        }

        return $query;
    }
}
