<?php

namespace App\Filament\Resources;

use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Filament\Resources\RoleResource\Pages;
use BezhanSalleh\FilamentShield\Support\Utils;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationGroup = 'User Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->label('Role Name'),
                        Forms\Components\TextInput::make('guard_name')
                            ->default('web')
                            ->required()
                            ->label('Guard'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Permissions')
                    ->schema([
                        static::getPermissionFormSchema(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('guard_name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('permissions_count')
                    ->counts('permissions')
                    ->label('Permissions'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        // Start fresh query instead of using parent
        $query = static::getModel()::query();

        // Get current tenant
        $tenant = Filament::getTenant();

        if ($tenant) {
            // Scope to current tenant's roles only
            $query->where('company_id', $tenant->id);
        }

        return $query;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }

    protected static function getPermissionFormSchema(): Forms\Components\Component
    {
        $tenant = Filament::getTenant();
        if (!$tenant) {
            return Forms\Components\Placeholder::make('no_tenant')
                ->content('No tenant context available');
        }

        // Set tenant context for permissions
        setPermissionsTeamId($tenant->id);

        // Get all permissions and group by resource
        $permissions = Permission::all()->groupBy(function ($permission) {
            $parts = explode('_', $permission->name);
            if (count($parts) >= 3) {
                return implode('_', array_slice($parts, 2)); // Get resource name
            }
            return 'other';
        });

        $tabs = [];

        foreach ($permissions as $resource => $perms) {
            $resourceName = str_replace('_', ' ', ucwords(str_replace('_', ' ', $resource)));

            $tabs[] = Forms\Components\Tabs\Tab::make($resource)
                ->label($resourceName)
                ->schema([
                    Forms\Components\CheckboxList::make('permissions')
                        ->options($perms->pluck('name', 'name')->toArray())
                        ->columns(2)
                        ->hiddenLabel(),
                ]);
        }

        return Forms\Components\Tabs::make('Permissions')
            ->tabs($tabs);
    }


}
