<?php

namespace App\Filament\Resources;

use BezhanSalleh\FilamentShield\Resources\RoleResource as ShieldRoleResource;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Role;

class RoleResource extends ShieldRoleResource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationGroup = 'User Management';

    protected static ?int $navigationSort = 1;

    public static function getEloquentQuery(): Builder
    {
        // Start fresh query instead of using parent
        $query = static::getModel()::query();

        // Get current tenant
        $tenant = Filament::getTenant();

        if ($tenant) {
            // Scope to current tenant's roles only
            $query->where('company_id', $tenant->id);

            // Debug: Log what we're querying
            Log::info('Role Resource Query', [
                'tenant_id' => $tenant->id,
                'tenant_name' => $tenant->name,
                'sql' => $query->toSql(),
                'bindings' => $query->getBindings(),
                'count' => $query->count()
            ]);
        }

        return $query;
    }

    public static function shouldRegisterNavigation(): bool
    {
        return true;
    }
}
