<?php

namespace App\Filament\Resources;

use BezhanSalleh\FilamentShield\Resources\RoleResource as ShieldRoleResource;
use Illuminate\Database\Eloquent\Builder;
use Filament\Facades\Filament;

class RoleResource extends ShieldRoleResource
{
    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Remove any existing constraints and apply only tenant scoping
        $query->getQuery()->wheres = [];
        $query->getQuery()->bindings['where'] = [];

        // Get current tenant
        $tenant = Filament::getTenant();

        if ($tenant) {
            // Scope to current tenant's roles only - show ALL tenant roles regardless of user assignment
            $query->where('company_id', $tenant->id);
        }

        return $query;
    }
}
