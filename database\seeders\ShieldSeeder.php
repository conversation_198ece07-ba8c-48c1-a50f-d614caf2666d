<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON><PERSON><PERSON><PERSON>alleh\FilamentShield\Support\Utils;
use Spatie\Permission\PermissionRegistrar;

class ShieldSeeder extends Seeder
{
    public function run(): void
    {
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        $rolesWithPermissions = '[{"name":"panel_user","guard_name":"web","permissions":[]},{"name":"super_admin","guard_name":"web","permissions":["view_bank","view_any_bank","create_bank","update_bank","restore_bank","restore_any_bank","replicate_bank","reorder_bank","delete_bank","delete_any_bank","force_delete_bank","force_delete_any_bank","view_bank::location","view_any_bank::location","create_bank::location","update_bank::location","restore_bank::location","restore_any_bank::location","replicate_bank::location","reorder_bank::location","delete_bank::location","delete_any_bank::location","force_delete_bank::location","force_delete_any_bank::location","view_branch","view_any_branch","create_branch","update_branch","restore_branch","restore_any_branch","replicate_branch","reorder_branch","delete_branch","delete_any_branch","force_delete_branch","force_delete_any_branch","view_deposit::account","view_any_deposit::account","create_deposit::account","update_deposit::account","restore_deposit::account","restore_any_deposit::account","replicate_deposit::account","reorder_deposit::account","delete_deposit::account","delete_any_deposit::account","force_delete_deposit::account","force_delete_any_deposit::account","view_fixed::deposit","view_any_fixed::deposit","create_fixed::deposit","update_fixed::deposit","restore_fixed::deposit","restore_any_fixed::deposit","replicate_fixed::deposit","reorder_fixed::deposit","delete_fixed::deposit","delete_any_fixed::deposit","force_delete_fixed::deposit","force_delete_any_fixed::deposit","view_role","view_any_role","create_role","update_role","delete_role","delete_any_role","view_user","view_any_user","create_user","update_user","restore_user","restore_any_user","replicate_user","reorder_user","delete_user","delete_any_user","force_delete_user","force_delete_any_user"]}]';
        $directPermissions = '[]';

        static::makeRolesWithPermissions($rolesWithPermissions);
        static::makeDirectPermissions($directPermissions);

        $this->command->info('Shield Seeding Completed.');
    }

    protected static function makeRolesWithPermissions(string $rolesWithPermissions): void
    {
        if (! blank($rolePlusPermissions = json_decode($rolesWithPermissions, true))) {
            /** @var Model $roleModel */
            $roleModel = Utils::getRoleModel();
            /** @var Model $permissionModel */
            $permissionModel = Utils::getPermissionModel();

            foreach ($rolePlusPermissions as $rolePlusPermission) {
                $role = $roleModel::firstOrCreate([
                    'name' => $rolePlusPermission['name'],
                    'guard_name' => $rolePlusPermission['guard_name'],
                ]);

                if (! blank($rolePlusPermission['permissions'])) {
                    $permissionModels = collect($rolePlusPermission['permissions'])
                        ->map(fn ($permission) => $permissionModel::firstOrCreate([
                            'name' => $permission,
                            'guard_name' => $rolePlusPermission['guard_name'],
                        ]))
                        ->all();

                    $role->syncPermissions($permissionModels);
                }
            }
        }
    }

    public static function makeDirectPermissions(string $directPermissions): void
    {
        if (! blank($permissions = json_decode($directPermissions, true))) {
            /** @var Model $permissionModel */
            $permissionModel = Utils::getPermissionModel();

            foreach ($permissions as $permission) {
                if ($permissionModel::whereName($permission)->doesntExist()) {
                    $permissionModel::create([
                        'name' => $permission['name'],
                        'guard_name' => $permission['guard_name'],
                    ]);
                }
            }
        }
    }
}
