<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use App\Models\Company;

class TestRoleAssignmentSeeder extends Seeder
{
    public function run(): void
    {
        $user = User::find(2); // Get user with ID 2
        $company = Company::find(1); // Get company with ID 1
        
        if (!$user || !$company) {
            $this->command->error('User ID 2 or Company ID 1 not found');
            return;
        }
        
        // Set tenant context
        setPermissionsTeamId($company->id);
        
        // Get roles for this company
        $roles = Role::where('company_id', $company->id)->get();
        
        $this->command->info("Available roles for company {$company->name}:");
        foreach ($roles as $role) {
            $this->command->line("- {$role->name} (ID: {$role->id})");
        }
        
        // Test getting user roles with proper table qualification
        $userRoles = $user->roles()
            ->where('roles.company_id', $company->id)
            ->get();
            
        $this->command->info("User {$user->name} current roles in {$company->name}:");
        foreach ($userRoles as $role) {
            $this->command->line("- {$role->name} (ID: {$role->id})");
        }
        
        $this->command->info('✅ Role query test completed successfully!');
    }
}
