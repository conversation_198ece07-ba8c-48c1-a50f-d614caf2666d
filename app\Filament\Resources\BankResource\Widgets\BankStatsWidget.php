<?php

namespace App\Filament\Resources\BankResource\Widgets;

use App\Models\Bank;
use App\Models\BankLocation;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class BankStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total Banks', Bank::count())
                ->description('Registered banks')
                ->descriptionIcon('heroicon-m-building-library')
                ->color('success'),

            Stat::make('Active Banks', Bank::whereHas('branches')->count())
                ->description('Banks with locations')
                ->descriptionIcon('heroicon-m-map-pin')
                ->color('info'),

            Stat::make('Bank Locations', BankLocation::count())
                ->description('Total branches')
                ->descriptionIcon('heroicon-m-building-office')
                ->color('warning'),
        ];
    }
}
