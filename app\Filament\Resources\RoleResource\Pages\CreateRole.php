<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use Filament\Facades\Filament;
use Filament\Resources\Pages\CreateRecord;

class CreateRole extends CreateRecord
{
    protected static string $resource = RoleResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set the company_id to current tenant
        $tenant = Filament::getTenant();
        if ($tenant) {
            $data['company_id'] = $tenant->id;
            // Set tenant context for permissions
            setPermissionsTeamId($tenant->id);
        }

        return $data;
    }

    protected function afterCreate(): void
    {
        $tenant = Filament::getTenant();
        if ($tenant) {
            setPermissionsTeamId($tenant->id);
        }

        // Get permissions from form data
        $permissions = collect($this->form->getState())
            ->filter(function ($value, $key) {
                return $key === 'permissions' && is_array($value);
            })
            ->flatten()
            ->filter()
            ->toArray();

        if (!empty($permissions)) {
            $this->record->syncPermissions($permissions);
        }
    }
}
