<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use Filament\Resources\Pages\CreateRecord;
use Filament\Facades\Filament;

class CreateRole extends CreateRecord
{
    protected static string $resource = RoleResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set the company_id to current tenant
        $tenant = Filament::getTenant();
        if ($tenant) {
            $data['company_id'] = $tenant->id;
        }

        return $data;
    }
}
