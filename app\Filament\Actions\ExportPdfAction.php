<?php

namespace App\Filament\Actions;

use App\Models\Company;
use App\Models\FixedDeposit;
use Barryvdh\DomPDF\Facade\Pdf;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;

class ExportPdfAction extends Action
{
    public static function getDefaultName(): string
    {
        return 'exportPdf';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Export PDF');
        $this->icon('heroicon-o-document-text');
        $this->color('danger');

        // Add a modal with filter options
        $this->form([
            // First row - three select fields in one line
            Grid::make(3)
                ->schema([
                    Select::make('company_id')
                        ->label('Company')
                        ->options(Company::pluck('company_name', 'id'))
                        ->searchable()
                        ->placeholder('All Companies'),

                    Select::make('deposit_type')
                        ->label('Deposit Type')
                        ->options([
                            'MUDHARABAH' => 'Mudharabah',
                            'CONVENTIONAL' => 'Conventional',
                        ])
                        ->placeholder('All Types'),

                    Select::make('status')
                        ->label('Status')
                        ->options([
                            'ACTIVE' => 'Active',
                            'MATURED' => 'Matured',
                            'WITHDRAWN' => 'Withdrawn',
                            'ROLLED_OVER' => 'Rolled Over',
                        ])
                        ->placeholder('All Statuses'),
                ]),

            // Second row - date pickers in one line
            Grid::make(2)
                ->schema([
                    DatePicker::make('from_date')
                        ->label('From Date')
                        ->placeholder('Any Date')
                        ->suffixIcon('heroicon-m-calendar')
                        ->displayFormat('d/m/Y')
                        ->native(false),

                    DatePicker::make('to_date')
                        ->label('To Date')
                        ->placeholder('Any Date')
                        ->suffixIcon('heroicon-m-calendar')
                        ->displayFormat('d/m/Y')
                        ->native(false),
                ]),
        ]);

        $this->modalHeading('Export Fixed Deposits to PDF');
        $this->modalDescription('Select filters to apply to the exported data.');
        $this->modalSubmitActionLabel('Generate PDF');

        $this->action(function (array $data) {
            // Get filtered records
            $records = $this->getFilteredRecords($data);

            if ($records->isEmpty()) {
                Notification::make()
                    ->title('No records to export')
                    ->warning()
                    ->send();

                return;
            }

            return $this->generatePdf($records, $data);
        });
    }

    protected function getFilteredRecords(array $data): Collection
    {
        // Start with the base query
        $query = FixedDeposit::query()
            ->with(['company', 'depositAccount.bankLocation.bank', 'depositAccount.bankLocation.branch']);

        // Apply company filter
        if (!empty($data['company_id'])) {
            $query->where('company_id', $data['company_id']);
        }

        // Apply deposit type filter
        if (!empty($data['deposit_type'])) {
            $query->where('deposit_type', $data['deposit_type']);
        }

        // Apply status filter
        if (!empty($data['status'])) {
            $query->where('status', $data['status']);
        }

        // Apply date range filter
        if (!empty($data['from_date']) && !empty($data['to_date'])) {
            // Find deposits where the period overlaps with the selected range
            $query->where(function (Builder $query) use ($data) {
                $query->where(function (Builder $query) use ($data) {
                    // Start date falls within range
                    $query->where('start_date', '>=', $data['from_date'])
                          ->where('start_date', '<=', $data['to_date']);
                })->orWhere(function (Builder $query) use ($data) {
                    // Maturity date falls within range
                    $query->where('maturity_date', '>=', $data['from_date'])
                          ->where('maturity_date', '<=', $data['to_date']);
                })->orWhere(function (Builder $query) use ($data) {
                    // Deposit spans the entire range
                    $query->where('start_date', '<=', $data['from_date'])
                          ->where('maturity_date', '>=', $data['to_date']);
                });
            });
        } elseif (!empty($data['from_date'])) {
            // Only from date is provided
            $query->where(function (Builder $query) use ($data) {
                $query->where('start_date', '>=', $data['from_date'])
                      ->orWhere('maturity_date', '>=', $data['from_date']);
            });
        } elseif (!empty($data['to_date'])) {
            // Only to date is provided
            $query->where(function (Builder $query) use ($data) {
                $query->where('start_date', '<=', $data['to_date'])
                      ->orWhere('maturity_date', '<=', $data['to_date']);
            });
        }

        // Get the records
        return $query->get();
    }

    protected function generatePdf(Collection $records, array $filters): mixed
    {
        // Generate a unique filename
        $filename = 'fixed-deposits-' . Str::random(8) . '.pdf';

        // Get company name and code from the first record or from the filter
        $companyInfo = 'All Companies';
        if (!empty($filters['company_id'])) {
            $company = Company::find($filters['company_id']);
            if ($company) {
                $companyInfo = $company->company_name;
                if ($company->company_code) {
                    $companyInfo .= " ({$company->company_code})";
                }
            }
        } elseif ($records->isNotEmpty() && $records->first()->company) {
            $company = $records->first()->company;
            $companyInfo = $company->company_name;
            if ($company->company_code) {
                $companyInfo .= " ({$company->company_code})";
            }
        }

        // Get deposit type from filter or records
        $depositType = 'All Types';
        if (!empty($filters['deposit_type'])) {
            $depositType = match ($filters['deposit_type']) {
                'MUDHARABAH' => 'Mudharabah',
                'CONVENTIONAL' => 'Conventional',
                default => $filters['deposit_type'],
            };
        } elseif ($records->pluck('deposit_type')->unique()->count() === 1) {
            $type = $records->first()->deposit_type;
            $depositType = match ($type) {
                'MUDHARABAH' => 'Mudharabah',
                'CONVENTIONAL' => 'Conventional',
                default => $type,
            };
        }

        // Create the PDF with landscape orientation
        $pdf = Pdf::loadView('pdfs.fixed-deposits', [
            'records' => $records,
            'date' => now()->format('d/m/Y'),
            'companyInfo' => $companyInfo,
            'depositType' => $depositType,
            'filters' => $this->formatFilters($filters),
        ]);

        $pdf->setOptions(['enable_php' => true]);

        // Set landscape orientation
        $pdf->setPaper('a4', 'landscape');

        // Return the PDF for download
        return response()->streamDownload(
            fn () => print($pdf->output()),
            $filename
        );
    }

    protected function formatFilters(array $filters): array
    {
        $formatted = [];

        // Format date range for display in the PDF
        if (!empty($filters['from_date']) || !empty($filters['to_date'])) {
            $dateRange = 'Date Range: ';
            if (!empty($filters['from_date'])) {
                $dateRange .= 'From ' . date('d/m/Y', strtotime($filters['from_date']));
            }
            if (!empty($filters['to_date'])) {
                $dateRange .= (!empty($filters['from_date']) ? ' ' : '') . 'To ' . date('d/m/Y', strtotime($filters['to_date']));
            }
            $formatted['date_range'] = $dateRange;
        }

        // Format status for display
        if (!empty($filters['status'])) {
            $formatted['status'] = 'Status: ' . match ($filters['status']) {
                'ACTIVE' => 'Active',
                'MATURED' => 'Matured',
                'WITHDRAWN' => 'Withdrawn',
                'ROLLED_OVER' => 'Rolled Over',
                default => $filters['status'],
            };
        }

        return $formatted;
    }
}






