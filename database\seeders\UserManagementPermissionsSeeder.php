<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use Spatie\Permission\Models\Permission;

class UserManagementPermissionsSeeder extends Seeder
{
    public function run(): void
    {
        // Set tenant context for company 1
        setPermissionsTeamId(1);
        
        // Get super_admin role
        $superAdminRole = Role::where('name', 'super_admin')
            ->where('company_id', 1)
            ->first();
            
        if ($superAdminRole) {
            // Get all user management permissions
            $permissions = Permission::where('name', 'like', '%user::management%')->get();
            
            // Assign permissions to super_admin role
            $superAdminRole->givePermissionTo($permissions);
            
            $this->command->info("Assigned {$permissions->count()} user management permissions to super_admin role");
        } else {
            $this->command->error("Super admin role not found for company 1");
        }
    }
}
