<?php

namespace App\Filament\Resources\FixedDepositResource\Widgets;

use App\Models\FixedDeposit;
use Filament\Widgets\ChartWidget;

class DepositStatusChart extends ChartWidget
{
    protected static ?string $heading = 'Deposit Status Distribution';
    protected static ?int $sort = 3;

    protected function getData(): array
    {
        $data = FixedDeposit::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status');

        return [
            'datasets' => [
                [
                    'data' => $data->values()->toArray(),
                    'backgroundColor' => [
                        '#10b981', // ACTIVE - green
                        '#f59e0b', // MATURED - yellow
                        '#ef4444', // WITHDRAWN - red
                        '#3b82f6', // ROLLED_OVER - blue
                    ],
                ],
            ],
            'labels' => $data->keys()->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }
}
