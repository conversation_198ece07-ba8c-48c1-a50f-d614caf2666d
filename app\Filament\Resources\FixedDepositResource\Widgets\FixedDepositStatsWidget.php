<?php

namespace App\Filament\Resources\FixedDepositResource\Widgets;

use App\Models\FixedDeposit;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class FixedDepositStatsWidget extends BaseWidget
{
    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        return [
            Stat::make('Total Fixed Deposits', FixedDeposit::count())
                ->description('All fixed deposits')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success'),

            Stat::make('Active Deposits', FixedDeposit::where('status', 'ACTIVE')->count())
                ->description('Currently active')
                ->descriptionIcon('heroicon-m-play-circle')
                ->color('info'),

            Stat::make('Total Principal',
                'RM ' . number_format(FixedDeposit::where('status', 'ACTIVE')->sum('principal_amount'), 2))
                ->description('Active deposits value')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('warning'),

            Stat::make('Maturing This Month',
                FixedDeposit::where('status', 'ACTIVE')
                    ->whereBetween('maturity_date', [
                        Carbon::now()->startOfMonth(),
                        Carbon::now()->endOfMonth()
                    ])->count())
                ->description('Deposits maturing soon')
                ->descriptionIcon('heroicon-m-clock')
                ->color('danger'),
        ];
    }
}
