<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;

class VerifyRolesSeeder extends Seeder
{
    public function run(): void
    {
        $roles = Role::all(['id', 'name', 'company_id']);
        
        $this->command->info('Current roles in database:');
        $this->command->table(
            ['ID', 'Name', 'Company ID'],
            $roles->map(fn($role) => [$role->id, $role->name, $role->company_id ?? 'NULL'])->toArray()
        );
        
        $nullCompanyRoles = Role::whereNull('company_id')->count();
        if ($nullCompanyRoles > 0) {
            $this->command->error("Found {$nullCompanyRoles} roles with NULL company_id!");
        } else {
            $this->command->info("✅ All roles have proper company_id assigned!");
        }
    }
}
