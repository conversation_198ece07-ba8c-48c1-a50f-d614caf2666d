<?php

require 'vendor/autoload.php';

$policies = [
    'DepositAccountPolicy',
    'FixedDepositPolicy', 
    'BankLocationPolicy'
];

foreach ($policies as $policyName) {
    $filePath = "app/Policies/{$policyName}.php";
    
    if (!file_exists($filePath)) {
        echo "File not found: {$filePath}\n";
        continue;
    }
    
    $content = file_get_contents($filePath);
    
    // Add the trait import
    if (!str_contains($content, 'use App\Traits\HasTenantPolicy;')) {
        $content = str_replace(
            'use Illuminate\Auth\Access\HandlesAuthorization;',
            "use App\Traits\HasTenantPolicy;\nuse Illuminate\Auth\Access\HandlesAuthorization;",
            $content
        );
    }
    
    // Add the trait usage
    if (!str_contains($content, 'HasTenantPolicy')) {
        $content = str_replace(
            'use HandlesAuthorization;',
            'use HandlesAuthorization, HasTenantPolicy;',
            $content
        );
    }
    
    // Fix viewAny method
    $content = preg_replace(
        '/public function viewAny\(User \$user\): bool\s*\{\s*return \$user->can\(\'([^\']+)\'\);\s*\}/',
        'public function viewAny(User $user): bool
    {
        return $this->checkPermission($user, \'$1\');
    }',
        $content
    );
    
    file_put_contents($filePath, $content);
    echo "Fixed: {$policyName}\n";
}

echo "All policies fixed!\n";
