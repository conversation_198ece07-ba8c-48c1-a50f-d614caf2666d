<?php

namespace App\Policies;

use App\Models\User;
use App\Traits\HasTenantPolicy;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserManagementPolicy
{
    use HandlesAuthorization, HasTenantPolicy;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $this->checkPermission($user, 'view_any_user::management');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, User $model): bool
    {
        return $this->checkPermission($user, 'view_user::management');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $this->checkPermission($user, 'create_user::management');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, User $model): bool
    {
        return $this->checkPermission($user, 'update_user::management');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, User $model): bool
    {
        return $this->checkPermission($user, 'delete_user::management');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $this->checkPermission($user, 'delete_any_user::management');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, User $model): bool
    {
        return $this->checkPermission($user, 'force_delete_user::management');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $this->checkPermission($user, 'force_delete_any_user::management');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, User $model): bool
    {
        return $this->checkPermission($user, 'restore_user::management');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $this->checkPermission($user, 'restore_any_user::management');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, User $model): bool
    {
        return $this->checkPermission($user, 'replicate_user::management');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $this->checkPermission($user, 'reorder_user::management');
    }
}
