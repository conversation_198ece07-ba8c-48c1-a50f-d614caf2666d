<?php

namespace App\Filament\Resources\DepositAccountResource\Widgets;

use App\Models\DepositAccount;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class DepositAccountStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total Accounts', DepositAccount::count())
                ->description('Deposit accounts')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success'),

            Stat::make('Active Accounts', DepositAccount::where('is_active', true)->count())
                ->description('Currently active')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('info'),

            Stat::make('Inactive Accounts', DepositAccount::where('is_active', false)->count())
                ->description('Inactive accounts')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color('danger'),
        ];
    }
}
