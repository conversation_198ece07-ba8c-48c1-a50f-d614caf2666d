<?php

namespace App\Filament\Resources\DepositAccountResource\Pages;

use App\Filament\Resources\DepositAccountResource;
use App\Filament\Resources\DepositAccountResource\Widgets\AccountsByBankChart;
use App\Filament\Resources\DepositAccountResource\Widgets\DepositAccountStatsWidget;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDepositAccounts extends ListRecords
{
    protected static string $resource = DepositAccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            DepositAccountStatsWidget::class,
            AccountsByBankChart::class
        ];
    }
}
