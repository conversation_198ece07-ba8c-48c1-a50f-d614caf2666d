<?php

namespace App\Filament\Resources\FixedDepositResource\Pages;

use App\Filament\Resources\FixedDepositResource;
use App\Filament\Resources\FixedDepositResource\Widgets\DepositStatusChart;
use App\Filament\Resources\FixedDepositResource\Widgets\DepositTypeChart;
use App\Filament\Resources\FixedDepositResource\Widgets\FixedDepositStatsWidget;
use App\Filament\Resources\FixedDepositResource\Widgets\MonthlyDepositsChart;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFixedDeposits extends ListRecords
{
    protected static string $resource = FixedDepositResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            FixedDepositStatsWidget::class,
            DepositTypeChart::class,
            DepositStatusChart::class,
            MonthlyDepositsChart::class
        ];
    }
}
