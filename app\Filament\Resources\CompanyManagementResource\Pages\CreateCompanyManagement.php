<?php

namespace App\Filament\Resources\CompanyManagementResource\Pages;

use App\Filament\Resources\CompanyManagementResource;
use App\Models\Role;
use Filament\Resources\Pages\CreateRecord;

class CreateCompanyManagement extends CreateRecord
{
    protected static string $resource = CompanyManagementResource::class;

    protected function afterCreate(): void
    {
        // After creating a company, create default roles for it
        $company = $this->record;
        
        // Set tenant context
        setPermissionsTeamId($company->id);
        
        // Create default roles
        $defaultRoles = [
            ['name' => 'super_admin', 'guard_name' => 'web'],
            ['name' => 'panel_user', 'guard_name' => 'web'],
        ];
        
        foreach ($defaultRoles as $roleData) {
            Role::firstOrCreate([
                'name' => $roleData['name'],
                'guard_name' => $roleData['guard_name'],
                'company_id' => $company->id,
            ]);
        }
        
        $this->notify('success', 'Company created successfully with default roles!');
    }
}
