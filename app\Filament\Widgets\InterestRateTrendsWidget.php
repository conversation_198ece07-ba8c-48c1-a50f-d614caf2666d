<?php

namespace App\Filament\Widgets;

use App\Models\FixedDeposit;
use Filament\Widgets\ChartWidget;

class InterestRateTrendsWidget extends ChartWidget
{
    protected static ?string $heading = 'Interest Rate Trends (Last 12 Months)';
    protected static ?int $sort = 4;
    protected int | string | array $columnSpan = 'full';

    protected function getData(): array
    {
        $months = collect();
        for ($i = 11; $i >= 0; $i--) {
            $months->push(now()->subMonths($i));
        }

        $mudharabahData = [];
        $conventionalData = [];
        $labels = [];

        foreach ($months as $month) {
            $monthStart = $month->startOfMonth()->toDateString();
            $monthEnd = $month->endOfMonth()->toDateString();

            $mudharabahAvg = FixedDeposit::where('deposit_type', 'MUDHARABAH')
                ->whereBetween('start_date', [$monthStart, $monthEnd])
                ->avg('interest_rate') ?: 0;

            $conventionalAvg = FixedDeposit::where('deposit_type', 'CONVENTIONAL')
                ->whereBetween('start_date', [$monthStart, $monthEnd])
                ->avg('interest_rate') ?: 0;

            $mudharabahData[] = round($mudharabahAvg, 2);
            $conventionalData[] = round($conventionalAvg, 2);
            $labels[] = $month->format('M Y');
        }

        return [
            'datasets' => [
                [
                    'label' => 'Mudharabah',
                    'data' => $mudharabahData,
                    'borderColor' => '#10B981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'fill' => true,
                ],
                [
                    'label' => 'Conventional',
                    'data' => $conventionalData,
                    'borderColor' => '#3B82F6',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => true,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }
}
