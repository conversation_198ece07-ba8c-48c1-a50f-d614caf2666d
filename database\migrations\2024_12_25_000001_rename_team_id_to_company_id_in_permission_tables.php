<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Rename team_id to company_id in roles table
        if (Schema::hasColumn('roles', 'team_id')) {
            Schema::table('roles', function (Blueprint $table) {
                $table->renameColumn('team_id', 'company_id');
            });
        }

        // Rename team_id to company_id in model_has_roles table
        if (Schema::hasColumn('model_has_roles', 'team_id')) {
            Schema::table('model_has_roles', function (Blueprint $table) {
                $table->renameColumn('team_id', 'company_id');
            });
        }

        // Rename team_id to company_id in model_has_permissions table
        if (Schema::hasColumn('model_has_permissions', 'team_id')) {
            Schema::table('model_has_permissions', function (Blueprint $table) {
                $table->renameColumn('team_id', 'company_id');
            });
        }

        // Rename team_id to company_id in role_has_permissions table
        if (Schema::hasColumn('role_has_permissions', 'team_id')) {
            Schema::table('role_has_permissions', function (Blueprint $table) {
                $table->renameColumn('team_id', 'company_id');
            });
        }
    }

    public function down(): void
    {
        // Rename company_id back to team_id in roles table
        if (Schema::hasColumn('roles', 'company_id')) {
            Schema::table('roles', function (Blueprint $table) {
                $table->renameColumn('company_id', 'team_id');
            });
        }

        // Rename company_id back to team_id in model_has_roles table
        if (Schema::hasColumn('model_has_roles', 'company_id')) {
            Schema::table('model_has_roles', function (Blueprint $table) {
                $table->renameColumn('company_id', 'team_id');
            });
        }

        // Rename company_id back to team_id in model_has_permissions table
        if (Schema::hasColumn('model_has_permissions', 'company_id')) {
            Schema::table('model_has_permissions', function (Blueprint $table) {
                $table->renameColumn('company_id', 'team_id');
            });
        }

        // Rename company_id back to team_id in role_has_permissions table
        if (Schema::hasColumn('role_has_permissions', 'company_id')) {
            Schema::table('role_has_permissions', function (Blueprint $table) {
                $table->renameColumn('company_id', 'team_id');
            });
        }
    }
};
