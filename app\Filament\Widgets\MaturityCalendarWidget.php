<?php

namespace App\Filament\Widgets;

use App\Models\FixedDeposit;
use Filament\Widgets\Widget;

class MaturityCalendarWidget extends Widget
{
    protected static string $view = 'filament.widgets.maturity-calendar-widget';
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';

    public function getViewData(): array
    {
        $upcomingMaturities = FixedDeposit::with(['depositAccount.company', 'depositAccount.bankLocation.bank'])
            ->where('status', 'ACTIVE')
            ->where('maturity_date', '>=', now())
            ->where('maturity_date', '<=', now()->addDays(90))
            ->orderBy('maturity_date')
            ->get()
            ->map(function ($deposit) {
                return [
                    'id' => $deposit->id,
                    'receipt_number' => $deposit->receipt_number,
                    'company' => $deposit->depositAccount->company->company_name,
                    'bank' => $deposit->depositAccount->bankLocation->bank->bank_name,
                    'amount' => $deposit->principal_amount,
                    'maturity_date' => $deposit->maturity_date,
                    'days_to_maturity' => now()->diffInDays($deposit->maturity_date),
                    'urgency' => $this->getUrgencyLevel($deposit->maturity_date),
                ];
            });

        return [
            'maturities' => $upcomingMaturities,
        ];
    }

    private function getUrgencyLevel($maturityDate): string
    {
        $days = now()->diffInDays($maturityDate);

        if ($days <= 7) return 'critical';
        if ($days <= 30) return 'warning';
        return 'normal';
    }
}
