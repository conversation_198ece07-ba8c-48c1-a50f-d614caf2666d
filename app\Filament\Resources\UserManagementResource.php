<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserManagementResource\Pages;
use App\Models\User;
use App\Models\Company;
use App\Models\Role;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Facades\Filament;

class UserManagementResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'User Management';

    protected static ?string $navigationLabel = 'User Management';

    protected static ?string $modelLabel = 'User Management';

    protected static ?string $pluralModelLabel = 'User Management';

    // Disable tenant scoping for this resource - we want to see ALL users
    protected static ?string $tenantOwnershipRelationshipName = null;

    // Override to show ALL users across all tenants
    public static function getEloquentQuery(): Builder
    {
        // Return all users without any tenant filtering - bypass parent completely
        return static::getModel()::query();
    }

    // Override to disable tenant scoping completely
    public static function isScopedToTenant(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('User Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('password')
                            ->password()
                            ->required(fn (string $context): bool => $context === 'create')
                            ->maxLength(255)
                            ->dehydrated(fn ($state) => filled($state))
                            ->dehydrateStateUsing(fn ($state) => bcrypt($state)),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Company Assignments')
                    ->schema([
                        Forms\Components\Select::make('companies')
                            ->relationship('companies', 'company_name')
                            ->multiple()
                            ->preload()
                            ->searchable()
                            ->helperText('Select which companies this user can access'),
                    ]),

                Forms\Components\Section::make('Role Assignments')
                    ->schema([
                        Forms\Components\Repeater::make('role_assignments')
                            ->schema([
                                Forms\Components\Select::make('company_id')
                                    ->label('Company')
                                    ->options(Company::pluck('company_name', 'id'))
                                    ->required()
                                    ->reactive()
                                    ->searchable(),
                                Forms\Components\Select::make('roles')
                                    ->label('Roles')
                                    ->options(function (callable $get) {
                                        $companyId = $get('company_id');
                                        if (!$companyId) {
                                            return [];
                                        }
                                        return Role::where('company_id', $companyId)->pluck('name', 'id');
                                    })
                                    ->multiple()
                                    ->searchable()
                                    ->helperText('Roles for this specific company'),
                            ])
                            ->columns(2)
                            ->helperText('Assign specific roles to this user for each company')
                            ->defaultItems(0)
                            ->addActionLabel('Add Role Assignment')
                            ->dehydrated(false), // We'll handle this manually
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        $currentTenant = Filament::getTenant();

        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('companies.company_name')
                    ->badge()
                    ->separator(',')
                    ->label('Companies')
                    ->color(fn ($record) =>
                        $record->companies->contains('id', $currentTenant?->id) ? 'success' : 'gray'
                    ),
                Tables\Columns\TextColumn::make('current_tenant_roles')
                    ->label('Roles in Current Tenant')
                    ->badge()
                    ->separator(',')
                    ->getStateUsing(function ($record) use ($currentTenant) {
                        if (!$currentTenant) return [];

                        // Set tenant context and get roles
                        setPermissionsTeamId($currentTenant->id);
                        return $record->roles()->pluck('name')->toArray();
                    })
                    ->color('primary'),
                Tables\Columns\IconColumn::make('is_in_current_tenant')
                    ->label('In Current Tenant')
                    ->boolean()
                    ->getStateUsing(fn ($record) =>
                        $record->companies->contains('id', $currentTenant?->id)
                    ),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('companies')
                    ->relationship('companies', 'company_name')
                    ->multiple()
                    ->label('Filter by Company'),
                Tables\Filters\TernaryFilter::make('is_in_current_tenant')
                    ->label('In Current Tenant')
                    ->queries(
                        true: fn (Builder $query) => $query->whereHas('companies', fn ($q) => $q->where('companies.id', $currentTenant?->id)),
                        false: fn (Builder $query) => $query->whereDoesntHave('companies', fn ($q) => $q->where('companies.id', $currentTenant?->id)),
                    ),
            ])
            ->actions([
                Tables\Actions\Action::make('assign_to_tenant')
                    ->label('Assign to Current Tenant')
                    ->icon('heroicon-o-plus-circle')
                    ->color('success')
                    ->visible(fn ($record) => !$record->companies->contains('id', $currentTenant?->id))
                    ->action(function ($record) use ($currentTenant) {
                        if ($currentTenant) {
                            $record->companies()->attach($currentTenant->id);
                        }
                    })
                    ->requiresConfirmation(),
                Tables\Actions\Action::make('remove_from_tenant')
                    ->label('Remove from Current Tenant')
                    ->icon('heroicon-o-minus-circle')
                    ->color('danger')
                    ->visible(fn ($record) => $record->companies->contains('id', $currentTenant?->id))
                    ->action(function ($record) use ($currentTenant) {
                        if ($currentTenant) {
                            $record->companies()->detach($currentTenant->id);
                            // Also remove all roles for this tenant
                            setPermissionsTeamId($currentTenant->id);
                            $record->roles()->detach();
                        }
                    })
                    ->requiresConfirmation(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('assign_to_current_tenant')
                        ->label('Assign to Current Tenant')
                        ->icon('heroicon-o-plus-circle')
                        ->color('success')
                        ->action(function ($records) use ($currentTenant) {
                            if ($currentTenant) {
                                foreach ($records as $record) {
                                    if (!$record->companies->contains('id', $currentTenant->id)) {
                                        $record->companies()->attach($currentTenant->id);
                                    }
                                }
                            }
                        })
                        ->requiresConfirmation(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserManagement::route('/'),
            'create' => Pages\CreateUserManagement::route('/create'),
            'edit' => Pages\EditUserManagement::route('/{record}/edit'),
        ];
    }
}
