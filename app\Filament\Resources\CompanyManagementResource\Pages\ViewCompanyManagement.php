<?php

namespace App\Filament\Resources\CompanyManagementResource\Pages;

use App\Filament\Resources\CompanyManagementResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewCompanyManagement extends ViewRecord
{
    protected static string $resource = CompanyManagementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
