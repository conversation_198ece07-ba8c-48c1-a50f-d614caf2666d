<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            Portfolio Summary
        </x-slot>

        @php $data = $this->getViewData(); @endphp

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Overview</h4>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Total Deposits:</span>
                        <span class="font-medium">{{ number_format($data['summary']['total_deposits']) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Total Value:</span>
                        <span class="font-medium">RM {{ number_format($data['summary']['total_value'], 2) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Avg Tenure:</span>
                        <span class="font-medium">{{ number_format($data['summary']['avg_tenure']) }} days</span>
                    </div>
                </div>
            </div>

            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">By Type</h4>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Mudharabah:</span>
                        <span class="font-medium">{{ number_format($data['summary']['mudharabah_count']) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Conventional:</span>
                        <span class="font-medium">{{ number_format($data['summary']['conventional_count']) }}</span>
                    </div>
                </div>
            </div>

            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Interest Rates</h4>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Highest:</span>
                        <span class="font-medium">{{ number_format($data['summary']['highest_rate'], 2) }}%</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Lowest:</span>
                        <span class="font-medium">{{ number_format($data['summary']['lowest_rate'], 2) }}%</span>
                    </div>
                </div>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
