<?php

namespace App\Policies;

use App\Models\User;
use App\Models\FixedDeposit;
use App\Traits\HasTenantPolicy;
use Illuminate\Auth\Access\HandlesAuthorization;

class FixedDepositPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $this->checkPermission($user, 'view_any_fixed::deposit');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, FixedDeposit $fixedDeposit): bool
    {
        return $user->can('view_fixed::deposit');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_fixed::deposit');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, FixedDeposit $fixedDeposit): bool
    {
        return $user->can('update_fixed::deposit');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, FixedDeposit $fixedDeposit): bool
    {
        return $user->can('delete_fixed::deposit');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_fixed::deposit');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, FixedDeposit $fixedDeposit): bool
    {
        return $user->can('force_delete_fixed::deposit');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can('force_delete_any_fixed::deposit');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, FixedDeposit $fixedDeposit): bool
    {
        return $user->can('restore_fixed::deposit');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can('restore_any_fixed::deposit');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, FixedDeposit $fixedDeposit): bool
    {
        return $user->can('replicate_fixed::deposit');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $user->can('reorder_fixed::deposit');
    }
}
