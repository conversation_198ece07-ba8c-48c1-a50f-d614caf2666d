<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Permission\Models\Role as SpatieRole;
use Filament\Facades\Filament;

class Role extends SpatieRole
{
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Apply global scope for tenant filtering when in Filament context
     */
    protected static function booted()
    {
        parent::booted();

        static::addGlobalScope('tenant', function (Builder $builder) {
            // Only apply tenant scoping in Filament context
            if (app()->runningInConsole()) {
                return;
            }

            try {
                $tenant = Filament::getTenant();
                if ($tenant) {
                    $builder->where('company_id', $tenant->id);
                }
            } catch (\Exception) {
                // Ignore if not in Filament context
            }
        });
    }
}
