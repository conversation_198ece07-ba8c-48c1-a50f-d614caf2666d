<?php

namespace App\Filament\Resources\BankLocationResource\Pages;

use App\Filament\Resources\BankLocationResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBankLocations extends ListRecords
{
    protected static string $resource = BankLocationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
