<?php

namespace App\Filament\Resources\BankResource\Pages;

use App\Filament\Resources\BankResource;
use App\Filament\Resources\BankResource\Widgets\BankDistributionChart;
use App\Filament\Resources\BankResource\Widgets\BankStatsWidget;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBanks extends ListRecords
{
    protected static string $resource = BankResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            BankStatsWidget::class,
            BankDistributionChart::class
        ];
    }
}
