<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            Upcoming Maturities (Next 90 Days)
        </x-slot>

        <div class="space-y-4">
            @forelse ($this->getViewData()['maturities'] as $maturity)
                @php
                    $urgencyColors = match ($maturity['urgency']) {
                        'critical' => 'border-red-200 bg-red-50 text-red-600',
                        'warning' => 'border-yellow-200 bg-yellow-50 text-yellow-600',
                        default => 'border-gray-200 bg-gray-50 text-gray-600',
                    };
                @endphp

                <div
                    class="flex items-center justify-between p-3 rounded-lg border {{ Str::before($urgencyColors, 'text-') }}">
                    <div class="flex-1">
                        <div class="font-medium">{{ $maturity['receipt_number'] }}</div>
                        <div class="text-sm text-gray-600">
                            {{ $maturity['company'] }} - {{ $maturity['bank'] }}
                        </div>
                        <div class="text-sm font-medium">RM {{ number_format($maturity['amount'], 2) }}</div>
                    </div>

                    <div class="text-right">
                        <div class="font-medium">{{ $maturity['maturity_date']->format('M j, Y') }}</div>
                        <div class="text-sm {{ Str::after($urgencyColors, 'text-') }}">
                            {{ $maturity['days_to_maturity'] }} days
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-8 text-gray-500">
                    No deposits maturing in the next 90 days
                </div>
            @endforelse
        </div>

    </x-filament::section>
</x-filament-widgets::widget>
