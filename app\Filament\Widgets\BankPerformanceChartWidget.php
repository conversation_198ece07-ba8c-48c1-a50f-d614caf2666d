<?php

namespace App\Filament\Widgets;

use App\Models\FixedDeposit;
use Illuminate\Support\Facades\DB;
use Filament\Widgets\ChartWidget;

class BankPerformanceChartWidget extends ChartWidget
{
    protected static ?string $heading = 'Deposits by Bank';
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';

    protected function getData(): array
    {
        $data = FixedDeposit::select(
                'banks.bank_name',
                DB::raw('COUNT(*) as deposit_count'),
                DB::raw('SUM(principal_amount) as total_amount'),
                DB::raw('AVG(interest_rate) as avg_rate')
            )
            ->join('deposit_accounts', 'fixed_deposits.deposit_account_id', '=', 'deposit_accounts.id')
            ->join('bank_locations', 'deposit_accounts.bank_location_id', '=', 'bank_locations.id')
            ->join('banks', 'bank_locations.bank_id', '=', 'banks.id')
            ->where('fixed_deposits.status', 'ACTIVE')
            ->groupBy('banks.id', 'banks.bank_name')
            ->orderBy('total_amount', 'desc')
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Total Amount (RM)',
                    'data' => $data->pluck('total_amount')->toArray(),
                    'backgroundColor' => [
                        '#10B981', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6',
                        '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
                    ],
                ],
            ],
            'labels' => $data->pluck('bank_name')->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
            ],
            'maintainAspectRatio' => false,
        ];
    }
}
